import { useState } from 'react';
import PostTable from './PostTable';
import PostCreate from './PostCreate';
import PostEdit from './PostEdit';
import './App.css';

const App = () => {
  const [currentView, setCurrentView] = useState('table');

  const renderCurrentView = () => {
    switch (currentView) {
      case 'create':
        return <PostCreate />;
      case 'edit':
        return <PostEdit />;
      case 'table':
      default:
        return (
          <div className="container">
            <div className="header">
              <h1>Post Management</h1>
              <button
                className="btn btn-primary"
                onClick={() => setCurrentView('create')}
              >
                + Create New Post
              </button>
            </div>
            <PostTable />
          </div>
        );
    }
  };

  return (
    <div className="app">
      {currentView !== 'table' && (
        <div className="nav-header">
          <button
            className="btn btn-secondary"
            onClick={() => setCurrentView('table')}
          >
            ← Back to Posts
          </button>
          <div className="nav-actions">
            <button
              className="btn btn-outline"
              onClick={() => setCurrentView('create')}
            >
              Create Post
            </button>
            <button
              className="btn btn-outline"
              onClick={() => setCurrentView('edit')}
            >
              Edit Post
            </button>
          </div>
        </div>
      )}
      {renderCurrentView()}
    </div>
  );
};

export default App;