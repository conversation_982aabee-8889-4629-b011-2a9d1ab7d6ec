import { useState } from 'react';
import PostTable from './PostTable';
import './App.css';

const App = () => {
  // Sample data for posts
  const [posts, setPosts] = useState([
    {
      id: 1,
      name: "Getting Started with React",
      description: "A comprehensive guide to building your first React application with modern best practices."
    },
    {
      id: 2,
      name: "JavaScript ES6 Features",
      description: "Exploring the latest JavaScript features including arrow functions, destructuring, and modules."
    },
    {
      id: 3,
      name: "CSS Grid Layout",
      description: "Master CSS Grid to create complex, responsive layouts with ease and flexibility."
    },
    {
      id: 4,
      name: "Node.js Backend Development",
      description: "Building scalable server-side applications using Node.js and Express framework."
    },
    {
      id: 5,
      name: "Database Design Principles",
      description: "Understanding relational database design, normalization, and optimization techniques."
    }
  ]);

  const handleEdit = (id) => {
    console.log(`Edit post with ID: ${id}`);
    // Add your edit logic here
    // For example, you could open a modal or navigate to an edit page
  };

  const handleDelete = (id) => {
    if (window.confirm('Are you sure you want to delete this post?')) {
      setPosts(posts.filter(post => post.id !== id));
      console.log(`Deleted post with ID: ${id}`);
    }
  };

  return (
    <div className="app">
      <div className="container">
        <h1>Post Management</h1>
        <PostTable
          posts={posts}
          onEdit={handleEdit}
          onDelete={handleDelete}
        />
      </div>
    </div>
  );
};

export default App;