import './PostTable.css';

const PostTable = () => {
  return (
    <div className="post-table-container">
      <table className="post-table">
        <thead>
          <tr>
            <th>ID</th>
            <th>Post Name</th>
            <th>Post Description</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>1</td>
            <td>Getting Started with React</td>
            <td>A comprehensive guide to building your first React application with modern best practices.</td>
            <td>
              <button className="btn btn-edit">✏️ Edit</button>
              <button className="btn btn-delete">🗑️ Delete</button>
            </td>
          </tr>
          <tr>
            <td>2</td>
            <td>JavaScript ES6 Features</td>
            <td>Exploring the latest JavaScript features including arrow functions, destructuring, and modules.</td>
            <td>
              <button className="btn btn-edit">✏️ Edit</button>
              <button className="btn btn-delete">🗑️ Delete</button>
            </td>
          </tr>
          <tr>
            <td>3</td>
            <td>CSS Grid Layout</td>
            <td>Master CSS Grid to create complex, responsive layouts with ease and flexibility.</td>
            <td>
              <button className="btn btn-edit">✏️ Edit</button>
              <button className="btn btn-delete">🗑️ Delete</button>
            </td>
          </tr>
          <tr>
            <td>4</td>
            <td>Node.js Backend Development</td>
            <td>Building scalable server-side applications using Node.js and Express framework.</td>
            <td>
              <button className="btn btn-edit">✏️ Edit</button>
              <button className="btn btn-delete">🗑️ Delete</button>
            </td>
          </tr>
          <tr>
            <td>5</td>
            <td>Database Design Principles</td>
            <td>Understanding relational database design, normalization, and optimization techniques.</td>
            <td>
              <button className="btn btn-edit">✏️ Edit</button>
              <button className="btn btn-delete">🗑️ Delete</button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  );
};

export default PostTable;
