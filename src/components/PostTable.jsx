import { useState } from 'react';
import './PostTable.css';

const PostTable = ({ posts, onEdit, onDelete }) => {
  const [sortField, setSortField] = useState(null);
  const [sortDirection, setSortDirection] = useState('asc');

  const handleSort = (field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const sortedPosts = [...posts].sort((a, b) => {
    if (!sortField) return 0;
    
    const aValue = a[sortField];
    const bValue = b[sortField];
    
    if (sortDirection === 'asc') {
      return aValue > bValue ? 1 : -1;
    } else {
      return aValue < bValue ? 1 : -1;
    }
  });

  const getSortIcon = (field) => {
    if (sortField !== field) return '↕️';
    return sortDirection === 'asc' ? '↑' : '↓';
  };

  return (
    <div className="post-table-container">
      <table className="post-table">
        <thead>
          <tr>
            <th 
              className="sortable" 
              onClick={() => handleSort('id')}
            >
              ID {getSortIcon('id')}
            </th>
            <th 
              className="sortable" 
              onClick={() => handleSort('name')}
            >
              Post Name {getSortIcon('name')}
            </th>
            <th 
              className="sortable" 
              onClick={() => handleSort('description')}
            >
              Post Description {getSortIcon('description')}
            </th>
            <th className="actions-header">Actions</th>
          </tr>
        </thead>
        <tbody>
          {sortedPosts.length === 0 ? (
            <tr>
              <td colSpan="4" className="no-data">
                No posts available
              </td>
            </tr>
          ) : (
            sortedPosts.map((post) => (
              <tr key={post.id} className="post-row">
                <td className="post-id">{post.id}</td>
                <td className="post-name">{post.name}</td>
                <td className="post-description">{post.description}</td>
                <td className="post-actions">
                  <button 
                    className="btn btn-edit"
                    onClick={() => onEdit(post.id)}
                    title="Edit post"
                  >
                    ✏️ Edit
                  </button>
                  <button 
                    className="btn btn-delete"
                    onClick={() => onDelete(post.id)}
                    title="Delete post"
                  >
                    🗑️ Delete
                  </button>
                </td>
              </tr>
            ))
          )}
        </tbody>
      </table>
      
      {posts.length > 0 && (
        <div className="table-footer">
          <p>Total posts: {posts.length}</p>
        </div>
      )}
    </div>
  );
};

export default PostTable;
