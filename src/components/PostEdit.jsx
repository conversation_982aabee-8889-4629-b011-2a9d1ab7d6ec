import { useState, useEffect } from 'react';
import './PostForm.css';

const PostEdit = ({ postId = 1 }) => {
  const [formData, setFormData] = useState({
    title: '',
    description: ''
  });

  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(true);

  // Sample data - replace with actual API call
  const samplePost = {
    id: 1,
    title: "Getting Started with React",
    description: "A comprehensive guide to building your first React application with modern best practices."
  };

  useEffect(() => {
    // Simulate loading existing post data
    setTimeout(() => {
      setFormData({
        title: samplePost.title,
        description: samplePost.description
      });
      setLoading(false);
    }, 500);
  }, [postId]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.title.trim()) {
      newErrors.title = 'Post title is required';
    }
    
    if (!formData.description.trim()) {
      newErrors.description = 'Post description is required';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    
    if (validateForm()) {
      console.log('Updating post:', { id: postId, ...formData });
      // Add your update logic here
      alert('Post updated successfully!');
    }
  };

  const handleCancel = () => {
    // Reset to original values
    setFormData({
      title: samplePost.title,
      description: samplePost.description
    });
    setErrors({});
    console.log('Edit cancelled');
  };

  if (loading) {
    return (
      <div className="post-form-container">
        <div className="post-form-wrapper">
          <div className="loading">Loading post data...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="post-form-container">
      <div className="post-form-wrapper">
        <h1 className="form-title">Edit Post</h1>
        
        <form onSubmit={handleSubmit} className="post-form">
          <div className="form-group">
            <label htmlFor="title" className="form-label">
              Post Title <span className="required">*</span>
            </label>
            <input
              type="text"
              id="title"
              name="title"
              value={formData.title}
              onChange={handleChange}
              className={`form-input ${errors.title ? 'error' : ''}`}
              placeholder="Enter post title"
            />
            {errors.title && <span className="error-message">{errors.title}</span>}
          </div>

          <div className="form-group">
            <label htmlFor="description" className="form-label">
              Post Description <span className="required">*</span>
            </label>
            <textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleChange}
              className={`form-textarea ${errors.description ? 'error' : ''}`}
              placeholder="Enter post description"
              rows="6"
            />
            {errors.description && <span className="error-message">{errors.description}</span>}
          </div>

          <div className="form-actions">
            <button type="button" onClick={handleCancel} className="btn btn-cancel">
              Cancel
            </button>
            <button type="submit" className="btn btn-primary">
              Update Post
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default PostEdit;
