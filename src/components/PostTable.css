/* PostTable.css */
.post-table-container {
  width: 100%;
  overflow-x: auto;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.post-table {
  width: 100%;
  border-collapse: collapse;
  background-color: white;
  font-size: 14px;
}

.post-table thead {
  background-color: #f8f9fa;
  border-bottom: 2px solid #dee2e6;
}

.post-table th {
  padding: 15px 12px;
  text-align: left;
  font-weight: 600;
  color: #495057;
  border-bottom: 1px solid #dee2e6;
  position: relative;
}



.post-table th:last-child {
  text-align: center;
  width: 200px;
}

.post-table td {
  padding: 12px;
  border-bottom: 1px solid #dee2e6;
  vertical-align: top;
}

.post-table tbody tr {
  transition: background-color 0.2s ease;
}

.post-table tbody tr:hover {
  background-color: #f8f9fa;
}

.post-table tbody tr:last-child td {
  border-bottom: none;
}

.post-table td:first-child {
  font-weight: 600;
  color: #6c757d;
  width: 80px;
  text-align: center;
}

.post-table td:nth-child(2) {
  font-weight: 500;
  color: #212529;
  max-width: 250px;
}

.post-table td:nth-child(3) {
  color: #6c757d;
  line-height: 1.4;
  max-width: 400px;
  word-wrap: break-word;
}

.post-table td:last-child {
  text-align: center;
  white-space: nowrap;
  width: 200px;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  margin: 0 4px;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn:active {
  transform: translateY(0);
}

.btn-edit {
  background-color: #007bff;
  color: white;
}

.btn-edit:hover {
  background-color: #0056b3;
}

.btn-delete {
  background-color: #dc3545;
  color: white;
}

.btn-delete:hover {
  background-color: #c82333;
}



/* Responsive design */
@media (max-width: 768px) {
  .post-table {
    font-size: 12px;
  }
  
  .post-table th,
  .post-table td {
    padding: 8px 6px;
  }
  
  .post-table td:nth-child(2) {
    max-width: 150px;
  }

  .post-table td:nth-child(3) {
    max-width: 200px;
  }
  
  .btn {
    padding: 6px 12px;
    font-size: 11px;
    margin: 0 2px;
  }
  
  .post-table td:last-child {
    width: 160px;
  }
}

@media (max-width: 480px) {
  .post-table-container {
    margin: 0 -15px;
  }
  
  .post-table th,
  .post-table td {
    padding: 6px 4px;
  }
  
  .btn {
    padding: 4px 8px;
    font-size: 10px;
  }
  
  .post-table td:last-child {
    width: 120px;
  }
  
  .btn span {
    display: none;
  }
}
